use candle_core::Tensor;
use candle_nn::{Init, VarBuilder};

#[derive(Clone)]
struct BertSelfOutput {
    dense: Linear,
    layer_norm: LayerNorm,
    dropout: Dropout,
    span: tracing::Span,
}

#[derive(<PERSON><PERSON>, Debug)]
pub struct DatasetVar {
    weight: Tensor,
}

impl DatasetVar {
    fn dataset_var(dataset: Tensor, vb: VarBuilder) -> anyhow::Result<DatasetVar> {
        // Create a parameter initialized with the dataset values
        // We need to convert dataset to initialization values
        let dims = dataset.dims();
        
        // Flatten and get values for initialization
        let flat_data = dataset.flatten_all()?.to_vec1::<f32>()?;
        
        // Create custom initialization
        let weight = vb.get_with_hints(
            dims,
            "dataset_weight", 
            Init::
        )?;
        
        Ok(DatasetVar { weight })
    }
}

impl BertSelfOutput {
   


    fn load(vb: VarBuilder, dataset: Tensor) -> anyhow::Result<Self> {
        let dense = candle_nn::linear(config.hidden_size, config.hidden_size, vb.pp("dense"))?;
        let layer_norm = layer_norm(
            config.hidden_size,
            config.layer_norm_eps,
            vb.pp("LayerNorm"),
        )?;
        let dropout = Dropout::new(config.hidden_dropout_prob);
        Ok(Self {
            dense,
            layer_norm,
            dropout,
            span: tracing::span!(tracing::Level::TRACE, "self-out"),
        })
    }

    fn forward(&self, hidden_states: &Tensor, input_tensor: &Tensor) -> Result<Tensor> {
        let _enter = self.span.enter();
        let hidden_states = self.dense.forward(hidden_states)?;
        let hidden_states = self.dropout.forward(&hidden_states)?;
        self.layer_norm.forward(&(hidden_states + input_tensor)?)
    }
}